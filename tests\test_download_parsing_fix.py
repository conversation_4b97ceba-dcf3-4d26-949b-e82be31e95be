#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试下载历史数据中的股票代码解析修复效果
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.下载历史数据 import _create_initial_result_file


def test_download_parsing_fix():
    """测试修复后的下载历史数据股票代码解析功能"""
    print("🧪 测试下载历史数据股票代码解析修复效果")
    print("="*50)
    
    # 测试文件路径
    test_file = os.path.join(project_root, "tests", "test_stock_list_with_comments.txt")
    result_file = os.path.join(project_root, "tests", "test_download_results.txt")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📋 测试文件: {test_file}")
    print(f"📄 结果文件: {result_file}")
    print()
    
    # 删除之前的结果文件
    if os.path.exists(result_file):
        os.remove(result_file)
    
    # 使用修复后的函数创建初始结果文件
    try:
        _create_initial_result_file(test_file, result_file)
        
        if os.path.exists(result_file):
            print("✅ 初始结果文件创建成功")
            
            # 读取并检查结果文件内容
            with open(result_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("\n📄 生成的结果文件内容预览:")
            lines = content.split('\n')
            for i, line in enumerate(lines[:30]):  # 只显示前30行
                print(f"  {i+1:2d}: {line}")
            
            if len(lines) > 30:
                print(f"  ... (共{len(lines)}行，仅显示前30行)")
            
            # 检查是否包含注释内容
            has_comments = '#' in content and '豆一主力连续合约' in content
            if has_comments:
                print("\n❌ 结果文件中仍包含注释内容")
            else:
                print("\n✅ 结果文件中不包含注释内容，解析正确")
            
            # 检查股票代码格式
            stock_codes = []
            in_stock_section = False
            for line in lines:
                line = line.strip()
                if "未下载的股票:" in line:
                    in_stock_section = True
                elif line.startswith("总计股票:"):
                    in_stock_section = False
                elif in_stock_section and line and "." in line:
                    stock_codes.append(line)
            
            print(f"\n📊 解析出的股票代码数量: {len(stock_codes)}")
            if stock_codes:
                print("📋 解析出的股票代码:")
                for i, code in enumerate(stock_codes, 1):
                    print(f"  {i}. {code}")
        else:
            print("❌ 初始结果文件创建失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*50)
    print("🎉 测试完成")


if __name__ == "__main__":
    test_download_parsing_fix()
