#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试股票代码解析修复效果
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.批量合成历史数据 import _read_stock_list_from_file


def test_stock_parsing_fix():
    """测试修复后的股票代码解析功能"""
    print("🧪 测试股票代码解析修复效果")
    print("="*50)
    
    # 测试文件路径
    test_file = os.path.join(project_root, "tests", "test_stock_list_with_comments.txt")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📋 测试文件: {test_file}")
    print()
    
    # 读取并显示原始文件内容
    print("📄 原始文件内容:")
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
    
    print("\n" + "="*50)
    print("🔍 解析结果:")
    
    # 使用修复后的函数解析股票代码
    stocks = _read_stock_list_from_file(test_file)
    
    print(f"\n✅ 解析成功，共获得 {len(stocks)} 个有效股票代码:")
    for i, stock in enumerate(stocks, 1):
        print(f"  {i}. {stock}")
    
    # 验证解析结果
    expected_codes = [
        "000001.SZ", "600000.SH", "000002.SZ",
        "A00.DF", "pg00.DF", "pp00.DF", 
        "cu00.DF", "rb00.DF",
        "000858.SZ", "600519.SH"
    ]
    
    print(f"\n🎯 预期股票代码数量: {len(expected_codes)}")
    print(f"📊 实际解析数量: {len(stocks)}")
    
    # 检查是否包含注释内容
    has_comments = any('#' in stock for stock in stocks)
    if has_comments:
        print("❌ 解析结果中仍包含注释内容")
        for stock in stocks:
            if '#' in stock:
                print(f"   包含注释的代码: {stock}")
    else:
        print("✅ 解析结果中不包含注释内容，解析正确")
    
    # 检查是否包含引号
    has_quotes = any('"' in stock or "'" in stock for stock in stocks)
    if has_quotes:
        print("❌ 解析结果中仍包含引号")
        for stock in stocks:
            if '"' in stock or "'" in stock:
                print(f"   包含引号的代码: {stock}")
    else:
        print("✅ 解析结果中不包含引号，解析正确")
    
    print("\n" + "="*50)
    print("🎉 测试完成")


if __name__ == "__main__":
    test_stock_parsing_fix()
