#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试合成即时保存功能

验证批量合成历史数据的即时保存机制是否正常工作
"""

import os
import sys
import time
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.core.synthesis_result_manager import SynthesisResultManager
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger(__name__, enhanced=True)


def test_synthesis_result_manager():
    """测试合成结果管理器的基本功能"""
    print("🧪 测试合成结果管理器基本功能")
    print("="*50)
    
    # 创建临时结果文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        temp_result_file = f.name
    
    try:
        # 初始化结果管理器
        manager = SynthesisResultManager(result_file=temp_result_file)
        
        # 测试保存成功结果
        print("📝 测试保存成功结果...")
        result1 = manager.save_synthesis_results(
            successful_symbols=['000001.SZ'],
            failed_symbols=[],
            not_synthesized_symbols=['000002.SZ', '600000.SH'],
            config_name='tick→1m合成',
            start_time='20250101',
            end_time='20250127'
        )
        print(f"✅ 保存结果: {result1}")
        
        # 测试保存失败结果
        print("\n📝 测试保存失败结果...")
        result2 = manager.save_synthesis_results(
            successful_symbols=[],
            failed_symbols=['000002.SZ'],
            not_synthesized_symbols=['600000.SH'],
            config_name='tick→1m合成',
            start_time='20250101',
            end_time='20250127',
            failed_reasons={'000002.SZ': '源数据不足'}
        )
        print(f"✅ 保存结果: {result2}")
        
        # 验证文件内容
        print("\n📄 验证结果文件内容...")
        with open(temp_result_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
        
        # 测试读取功能
        print("\n📖 测试读取未合成股票...")
        unsynthesized = manager.get_unsynthesized_stocks_from_file('tick→1m合成')
        print(f"✅ 未合成股票: {unsynthesized}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_result_file):
            os.unlink(temp_result_file)


def test_real_time_save_simulation():
    """模拟即时保存场景测试"""
    print("\n🧪 模拟即时保存场景测试")
    print("="*50)
    
    # 创建临时结果文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        temp_result_file = f.name
    
    try:
        manager = SynthesisResultManager(result_file=temp_result_file)
        
        # 模拟逐个股票处理
        test_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600519.SH']
        config_name = '1m→5m合成'
        
        print(f"📋 模拟处理 {len(test_stocks)} 只股票...")
        
        for i, stock in enumerate(test_stocks, 1):
            print(f"\n[{i}/{len(test_stocks)}] 处理: {stock}")
            
            # 模拟处理时间
            time.sleep(0.1)
            
            # 模拟成功/失败（第2只股票模拟失败）
            if i == 2:
                # 模拟失败
                print(f"❌ {stock} 处理失败")
                manager.save_synthesis_results(
                    successful_symbols=[],
                    failed_symbols=[stock],
                    not_synthesized_symbols=[],
                    config_name=config_name,
                    failed_reasons={stock: '数据格式错误'}
                )
            else:
                # 模拟成功
                print(f"✅ {stock} 处理成功")
                manager.save_synthesis_results(
                    successful_symbols=[stock],
                    failed_symbols=[],
                    not_synthesized_symbols=[],
                    config_name=config_name
                )
            
            print(f"💾 {stock} 结果已即时保存")
        
        # 验证最终结果
        print("\n📊 验证最终结果...")
        with open(temp_result_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
        
        # 验证统计信息
        lines = content.split('\n')
        success_count = 0
        failed_count = 0
        
        for line in lines:
            if line.startswith('合成成功:'):
                success_count = int(line.split(':')[1].strip())
            elif line.startswith('合成失败:'):
                failed_count = int(line.split(':')[1].strip())
        
        expected_success = 3  # 除了第2只股票，其他都成功
        expected_failed = 1   # 第2只股票失败
        
        if success_count == expected_success and failed_count == expected_failed:
            print(f"✅ 统计验证通过: 成功{success_count}只, 失败{failed_count}只")
            return True
        else:
            print(f"❌ 统计验证失败: 期望成功{expected_success}只失败{expected_failed}只, 实际成功{success_count}只失败{failed_count}只")
            return False
            
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_result_file):
            os.unlink(temp_result_file)


def test_interruption_recovery():
    """测试任务中断恢复场景"""
    print("\n🧪 测试任务中断恢复场景")
    print("="*50)
    
    # 创建临时结果文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        temp_result_file = f.name
    
    try:
        # 第一阶段：模拟部分处理后中断
        print("📝 第一阶段：模拟部分处理...")
        manager1 = SynthesisResultManager(result_file=temp_result_file)
        
        # 处理前3只股票
        stocks_phase1 = ['000001.SZ', '000002.SZ', '600000.SH']
        config_name = 'tick→1m合成'
        
        for stock in stocks_phase1:
            manager1.save_synthesis_results(
                successful_symbols=[stock],
                failed_symbols=[],
                not_synthesized_symbols=[],
                config_name=config_name
            )
            print(f"✅ {stock} 已处理并保存")
        
        print("⚠️  模拟任务中断...")
        
        # 第二阶段：模拟恢复后继续处理
        print("\n📝 第二阶段：模拟恢复后继续处理...")
        manager2 = SynthesisResultManager(result_file=temp_result_file)
        
        # 继续处理剩余股票
        stocks_phase2 = ['600519.SH', '000858.SZ']
        
        for stock in stocks_phase2:
            manager2.save_synthesis_results(
                successful_symbols=[stock],
                failed_symbols=[],
                not_synthesized_symbols=[],
                config_name=config_name
            )
            print(f"✅ {stock} 已处理并保存")
        
        # 验证恢复结果
        print("\n📊 验证恢复后的完整结果...")
        with open(temp_result_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
        
        # 验证所有股票都被正确记录
        all_stocks = stocks_phase1 + stocks_phase2
        success_section = False
        recorded_stocks = []
        
        for line in content.split('\n'):
            line = line.strip()
            if line == "合成成功的股票:":
                success_section = True
                continue
            elif line == "合成失败的股票:":
                success_section = False
                continue
            elif success_section and line and not line.startswith("=") and ":" not in line:
                recorded_stocks.append(line)
        
        if set(recorded_stocks) == set(all_stocks):
            print(f"✅ 恢复验证通过: 所有{len(all_stocks)}只股票都被正确记录")
            return True
        else:
            print(f"❌ 恢复验证失败: 期望{all_stocks}, 实际{recorded_stocks}")
            return False
            
    except Exception as e:
        print(f"❌ 中断恢复测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_result_file):
            os.unlink(temp_result_file)


def main():
    """主测试函数"""
    print("🚀 开始测试合成即时保存功能")
    print("="*60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("基本功能测试", test_synthesis_result_manager()))
    test_results.append(("即时保存模拟测试", test_real_time_save_simulation()))
    test_results.append(("中断恢复测试", test_interruption_recovery()))
    
    # 汇总测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！即时保存功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查即时保存功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
